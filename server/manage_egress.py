#!/usr/bin/env python3
"""
Utility script to manage LiveKit egress sessions.
This script helps you list and stop active egress recordings.
"""

import os
import asyncio
import argparse
from dotenv import load_dotenv
from livekit import api as lkapi
from storage import GCPEgressManager

load_dotenv()

async def list_egress_sessions():
    """List all active egress sessions."""
    api_client = lkapi.LiveKitAPI(
        os.getenv("LIVEKIT_API_URL"), 
        os.getenv("LIVEKIT_API_KEY"), 
        os.getenv("LIVEKIT_API_SECRET")
    )
    
    try:
        # List all egress sessions
        response = await api_client.egress.list_egress(lkapi.ListEgressRequest())
        egress_list = response.items if hasattr(response, 'items') else []

        if not egress_list:
            print("No active egress sessions found.")
            return []

        print(f"Found {len(egress_list)} egress session(s):")
        print("-" * 80)

        for i, egress in enumerate(egress_list, 1):
            print(f"{i}. Egress ID: {egress.egress_id}")
            print(f"   Room Name: {egress.room_name}")
            print(f"   Status: {egress.status}")
            print(f"   Started At: {egress.started_at}")
            if hasattr(egress, 'ended_at') and egress.ended_at:
                print(f"   Ended At: {egress.ended_at}")
            print("-" * 80)
        
        return egress_list
        
    except Exception as e:
        print(f"Error listing egress sessions: {e}")
        return []
    finally:
        await api_client.aclose()

async def stop_egress_session(egress_id: str):
    """Stop a specific egress session by ID."""
    api_client = lkapi.LiveKitAPI(
        os.getenv("LIVEKIT_API_URL"), 
        os.getenv("LIVEKIT_API_KEY"), 
        os.getenv("LIVEKIT_API_SECRET")
    )
    
    try:
        print(f"Stopping egress session: {egress_id}")
        request = lkapi.StopEgressRequest(egress_id=egress_id)
        await api_client.egress.stop_egress(request)
        print(f"Successfully stopped egress session: {egress_id}")

    except Exception as e:
        print(f"Error stopping egress session {egress_id}: {e}")
    finally:
        await api_client.aclose()

async def stop_all_egress_sessions():
    """Stop all active egress sessions."""
    egress_list = await list_egress_sessions()
    
    if not egress_list:
        return
    
    print("\nStopping all active egress sessions...")
    
    api_client = lkapi.LiveKitAPI(
        os.getenv("LIVEKIT_API_URL"), 
        os.getenv("LIVEKIT_API_KEY"), 
        os.getenv("LIVEKIT_API_SECRET")
    )
    
    try:
        for egress in egress_list:
            # Status 1 = EGRESS_STARTING, Status 2 = EGRESS_ACTIVE
            if egress.status in [1, 2]:  # Only stop active/starting sessions
                try:
                    request = lkapi.StopEgressRequest(egress_id=egress.egress_id)
                    await api_client.egress.stop_egress(request)
                    print(f"✓ Stopped egress session: {egress.egress_id}")
                except Exception as e:
                    print(f"✗ Failed to stop egress session {egress.egress_id}: {e}")
            else:
                print(f"- Skipping egress session {egress.egress_id} (status: {egress.status}, already ended)")
        
        print("\nFinished stopping egress sessions.")
        
    finally:
        await api_client.aclose()

async def main():
    parser = argparse.ArgumentParser(description="Manage LiveKit egress sessions")
    parser.add_argument("action", choices=["list", "stop", "stop-all"], 
                       help="Action to perform")
    parser.add_argument("--egress-id", help="Egress ID to stop (required for 'stop' action)")
    
    args = parser.parse_args()
    
    if args.action == "list":
        await list_egress_sessions()
    elif args.action == "stop":
        if not args.egress_id:
            print("Error: --egress-id is required for 'stop' action")
            return
        await stop_egress_session(args.egress_id)
    elif args.action == "stop-all":
        await stop_all_egress_sessions()

if __name__ == "__main__":
    asyncio.run(main())
