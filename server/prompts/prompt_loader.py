"""
Simple Prompt Loader for Interviewer Instructions

This module provides utilities to load and format prompt templates
from YAML configuration files.
"""

import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from jinja2 import Template


class InterviewerPromptLoader:
    """Loads and manages interviewer prompt templates."""

    def __init__(self, config_path: str = "prompts/interviewer_instructions.yml"):
        """
        Initialize the prompt loader.

        Args:
            config_path: Path to the YAML configuration file
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load the YAML configuration file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            raise FileNotFoundError(f"Prompt configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")

    def get_input_variables(self) -> List[str]:
        """Get the list of input variables required by the template."""
        return self.config.get("input_variables", [])

    def format_prompt(self, **kwargs) -> str:
        """
        Format the prompt with given variables using Jinja2 templating.

        Args:
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt string
        """
        # Validate required variables
        required_vars = set(self.get_input_variables())
        provided_vars = set(kwargs.keys())
        missing_vars = required_vars - provided_vars

        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")

        # Get the prompt template and format it using Jinja2
        prompt_template = self.config.get("prompt", "")
        template = Template(prompt_template)
        return template.render(**kwargs)

    def get_prompt_template(self) -> str:
        """Get the raw prompt template string."""
        return self.config.get("prompt", "")


def load_interviewer_prompt(config_path: Optional[str] = None) -> InterviewerPromptLoader:
    """
    Convenience function to load the interviewer prompt template.

    Args:
        config_path: Optional path to configuration file

    Returns:
        InterviewerPromptLoader instance
    """
    if config_path is None:
        config_path = "prompts/interviewer_instructions.yml"

    return InterviewerPromptLoader(config_path)


# Example usage
if __name__ == "__main__":
    # Load the prompt template
    prompt_loader = load_interviewer_prompt()

    print("Prompt loader created successfully!")
    print(f"Input variables: {prompt_loader.get_input_variables()}")

    # Example: Format a prompt with specific variables
    try:
        formatted_prompt = prompt_loader.format_prompt(
            role="Senior Software Engineer",
            level="Senior"
        )
        print("\nFormatted prompt:")
        print(formatted_prompt[:200] + "..." if len(formatted_prompt) > 200 else formatted_prompt)
    except ValueError as e:
        print(f"Error formatting prompt: {e}")

    # Example: Get raw template
    raw_template = prompt_loader.get_prompt_template()
    print(f"\nRaw template length: {len(raw_template)} characters")
