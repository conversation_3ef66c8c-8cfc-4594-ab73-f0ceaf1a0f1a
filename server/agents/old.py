import logging
import time
import asyncio
from livekit.agents import Agent, llm, tts
from livekit.plugins import google, silero
from tools import (
    end_interview_now,
    user_confirmed_end_interview
)
from livekit.agents.llm import function_tool

logger = logging.getLogger("gemini-agent")


class GeminiAgent(Agent):
    def __init__(self, role: str, level: str, name: str = "", topics_to_cover: str = "", interviewer_questions: str = "", company_info_available: str = "") -> None:
        """
        Initialize the agent with optimized configuration
        Args:
            role: The job role being interviewed for
            level: The experience level (Junior, Mid-level, Senior, etc.)
            name: The candidate's name for personalization
            topics_to_cover: Specific topics and fixed questions (newline separated)
            interviewer_questions: Specific questions the interviewer definitely wants to ask (newline separated)
            company_info_available: Job description content for targeted questioning
        """
        # Store all parameters for prompt generation
        self.role = role
        self.level = level
        self.name = name
        self.topics_to_cover = topics_to_cover
        self.interviewer_questions = interviewer_questions
        self.company_info_available = company_info_available
        self.is_initialized = False

        # Interview timing control
        self.interview_start_time = None
        self.target_duration_minutes = 30
        self.minimum_duration_minutes = 25  # Don't end before this
        self.timing_reminders_sent = set()  # Track which timing reminders we've sent
        self.timing_monitor_task = None  # Background task for timing enforcement

        # Silence monitoring for integrity - track when NO ONE is talking
        self.last_activity_time = None  # Track when anyone (agent or user) last spoke
        self.last_silence_warning_time = None  # Track when we last sent a silence warning
        self.silence_warnings_sent = set()
        self.silence_monitor_task = None
        self.agent_session = None  # Will be set when session starts

        # Transcript collection
        self.transcript_entries = []
        self.interview_start_time_for_transcript = None

        # Parse topics and fixed questions
        self.parsed_topics, self.fixed_questions = self._parse_topics_and_questions(topics_to_cover)

        # Parse interviewer questions (these are always treated as questions)
        self.parsed_interviewer_questions = self._parse_interviewer_questions(interviewer_questions)

        logger.info(f"Initializing GeminiAgent for {role} ({level}) - Candidate: {name or 'Anonymous'}")
        logger.info(f"Raw topics input: {topics_to_cover or 'None specified'}")
        logger.info(f"Raw interviewer questions input: {interviewer_questions or 'None specified'}")
        logger.info(f"Parsed topics: {self.parsed_topics}")
        logger.info(f"Fixed questions: {self.fixed_questions}")
        logger.info(f"Interviewer questions: {self.parsed_interviewer_questions}")
        logger.info(f"Company info available: {company_info_available}")

        recognition_config = {
    "language_code": "en-US",            # <-- lock to U.S. English
    "model": "latest_long",              # or whichever streaming model you prefer
    "enable_automatic_punctuation": True,
    "single_utterance": False,           # allow multi-turn responses
    # you can also tweak sensitivity, profanity filter, etc.
}

        # Get the system instructions directly
        instructions = self._get_system_instructions()

        # Initialize with optimized configuration and error handling
        try:
            super().__init__(
                instructions=instructions,
                llm=google.beta.realtime.RealtimeModel(
                    language="en-US",
                    voice="Aoede",
                ),
                vad=silero.VAD.load(),
                
                # Add function tools for enhanced capabilities
                tools=[
                    end_interview_now,
                    user_confirmed_end_interview,
                    self.save_current_transcript
                ]
            )
        except Exception as e:
            logger.error(f"Failed to initialize agent components: {e}")
            raise

        self.is_initialized = True
        logger.info("GeminiAgent initialized successfully")

    def _parse_topics_and_questions(self, topics_to_cover: str) -> tuple[list[str], list[str]]:
        """
        Parse the topics_to_cover string to separate topics from fixed questions.

        Args:
            topics_to_cover: Newline-separated string containing topics and questions

        Returns:
            Tuple of (topics_list, fixed_questions_list)
        """
        if not topics_to_cover.strip():
            return [], []

        lines = [line.strip() for line in topics_to_cover.split('\n') if line.strip()]
        topics = []
        fixed_questions = []

        # Common question patterns that indicate fixed questions
        question_indicators = [
            'why ', 'what ', 'how ', 'when ', 'where ', 'who ',
            'tell me about', 'describe', 'explain', 'can you',
            'have you', 'do you', 'would you', 'could you'
        ]

        for line in lines:
            line_lower = line.lower()

            # Check if this looks like a question
            is_question = (
                line.endswith('?') or
                any(line_lower.startswith(indicator) for indicator in question_indicators) or
                any(indicator in line_lower for indicator in ['why ', 'what is', 'how do'])
            )

            if is_question:
                fixed_questions.append(line)
                logger.debug(f"Identified as fixed question: '{line}'")
            else:
                topics.append(line)
                logger.debug(f"Identified as topic: '{line}'")

        return topics, fixed_questions

    def _parse_interviewer_questions(self, interviewer_questions: str) -> list[str]:
        """
        Parse the interviewer_questions string into a list of questions.

        Args:
            interviewer_questions: Newline-separated string containing questions

        Returns:
            List of questions
        """
        if not interviewer_questions.strip():
            return []

        lines = [line.strip() for line in interviewer_questions.split('\n') if line.strip()]
        questions = []

        for line in lines:
            # All lines in interviewer_questions are treated as questions
            questions.append(line)
            logger.debug(f"Added interviewer question: '{line}'")

        return questions

    def _get_system_instructions(self) -> str:
        """Get the system instructions directly without YAML template."""

        # Build the topics section
        topics_section = ""
        if self.parsed_topics:
            topics_list = ', '.join(self.parsed_topics)
            topics_section = f"""
### 2. Topics to Cover (8-10 minutes)
- **MANDATORY FIRST PRIORITY**: You MUST ask AT LEAST 2-3 DETAILED questions for EACH of these topics: {topics_list}
- **CRITICAL**: For EACH topic, ask multiple questions to thoroughly explore their knowledge:
  * Initial question: "Tell me about your experience with [topic]"
  * Follow-up: "How would you approach [specific scenario related to topic]?"
  * Deep dive: "What challenges have you faced with [topic] and how did you overcome them?"
"""

        # Build the fixed questions section
        fixed_questions_section = ""
        if self.fixed_questions:
            questions_list = '; '.join(self.fixed_questions)
            fixed_questions_section = f"""
### 3. Core Interview Questions (5-7 minutes)
- **MANDATORY SECOND PRIORITY**: You must ask ALL of these specific questions: {questions_list}
- ENHANCE and polish them to be more professional, grammatically correct, and contextually appropriate while preserving the core intent and meaning
- **NEVER mention these are "fixed questions" or "specific questions"** - ask them naturally as part of the interview flow
- **CRITICAL**: For EACH fixed question, ask AT LEAST 1-2 follow-up questions based on their responses - NO acknowledgments
"""

        # Build the interviewer questions section
        interviewer_questions_section = ""
        if self.parsed_interviewer_questions:
            interviewer_list = '; '.join(self.parsed_interviewer_questions)
            interviewer_questions_section = f"""
### 5. Interviewer Questions (3-5 minutes)
- **MANDATORY THIRD PRIORITY**: You must ask ALL of these interviewer questions: {interviewer_list}
- Ask them naturally as part of the interview flow
- **CRITICAL**: For EACH interviewer question, ask follow-up questions based on their responses
"""

        # Build the company info section
        company_section = ""
        if self.company_info_available:
            company_section = f"""
### 4. Job-Relevant Questions (5-7 minutes)
Based on this job description:
{self.company_info_available}

**MANDATORY QUESTIONS TO ASK:**
- For each technology mentioned: "How many years of experience do you have with [technology]?"
- For each skill listed: "Can you give me a specific example of when you used [skill]?"
- For each responsibility: "Tell me about a time when you had to [responsibility]"
- Explore their experience level: "Walk me through your most complex [technology] project"

**CRITICAL TONE INSTRUCTION:**
- **NEVER mention "job description", "requirements", or "this role needs"**
- **ALWAYS ask about their actual experience**: "What's your experience with [technology]?"
- **ASK, DON'T TELL**: Focus on discovering their experience, not stating requirements
"""

        # Build the main instructions
        instructions = f"""You are a highly skilled, AI-powered professional interviewer assigned to conduct a structured and dynamic 30-minute interview for the {self.role} position at the {self.level} level.

**CRITICAL TIMING REQUIREMENT**: This interview MUST last the full 30 minutes. Pace your questions accordingly and ensure you have enough content to fill the entire duration. Do not rush through questions or end early.

**CRITICAL INTERVIEWING STYLE**: You are NOT a friendly conversationalist - you are a PERSISTENT, PROBING interviewer who drills deep into every response. Your job is to CHALLENGE and CROSS-QUESTION every answer to uncover the candidate's true capabilities and experience depth.

## Core Responsibilities:
- Conduct a structured yet natural interview that covers both standard evaluation areas and custom requirements
- Evaluate problem-solving, communication, domain expertise, decision-making, and behavior under pressure
- **NEVER acknowledge or accept surface-level answers** - always drill deeper with follow-up questions
- **CROSS-QUESTION everything** - if they mention a project, ask about challenges, decisions, outcomes, alternatives
- **CHALLENGE their responses** - ask "why", "how", "what if" questions to test depth of knowledge

## Interview Structure (MANDATORY 30-minute flow):

### 1. Opening & Introduction (3-5 minutes)
- Greet the candidate warmly and professionally
- Introduce yourself as their interviewer for the {self.role} position
- Ask them to introduce themselves and provide a brief overview of their background
- **CRITICAL**: Ask multiple follow-up questions about their introduction:
  * "What drew you to [technology/field they mentioned]?"
  * "Tell me more about [specific experience they mentioned]"
  * "How did you get started with [skill/technology]?"
- **NEVER just acknowledge** - always ask for more details and specific examples

{topics_section}

{fixed_questions_section}

{company_section}

{interviewer_questions_section}

### 6. Candidate Questions (2-3 minutes)
- Ask: "Do you have any questions about the role, team, or company?"
- Answer their questions professionally and helpfully
- If they ask about projects, technologies, or company culture, provide informative responses

### 7. Closing (1-2 minutes)
- Thank them for their time and participation
- Inform them about next steps in the hiring process
- **CRITICAL**: End by saying "Thank you for your time today. Please click on the End Interview Button to finish the session."
- **IMMEDIATELY** call the end_interview_session tool after your closing remarks

## Critical Interviewing Techniques:

**DEEP DIVING REQUIREMENTS:**
- **NEVER accept the first answer** - always ask "Can you elaborate on that?" or "Tell me more about [specific aspect]"
- **For every project mentioned**: Ask about their specific role, challenges faced, decisions made, and outcomes achieved
- **For every technology mentioned**: Ask about years of experience, specific use cases, and complex scenarios they've handled
- **For every problem-solving example**: Ask about their thought process, alternatives considered, and lessons learned

**CROSS-QUESTIONING PATTERNS:**
- "You mentioned [X], can you walk me through a specific example?"
- "What was challenging about that situation?"
- "How did you decide between different approaches?"
- "What would you do differently if you faced that situation again?"
- "What was the outcome and how did you measure success?"

**INTEGRITY MONITORING:**
You have access to advanced monitoring capabilities to ensure interview integrity:

**Silence Detection**: If there's complete silence (no one speaking) for 30+ seconds, you will proactively address it:
- "I notice we've had some silence. Are you still there? Please continue with your response."
- Wait for their response before proceeding with the next question

**Available Tools:**

**save_current_transcript**: Use this tool to save the current interview transcript at any time. This tool will:
- Capture the full conversation history with timestamps
- Save it with proper metadata (role, level, candidate name, timestamp)
- Store it for later review and evaluation
- **IMPORTANT**: All conversation is automatically captured - you don't need to manually record anything



**end_interview_session**: Use this tool to send an RPC message to the frontend to end the interview when you have completed all interview sections. This tool will:
- Send a data message to the frontend client
- Trigger the frontend to end the interview and clean up
- Allow the frontend to handle UI state and navigation

**CRITICAL**: You MUST call save_current_transcript first, then end_interview_session immediately after saying your closing remarks. Do not wait for user action.

## Interview Protocols & Boundary Control

**Professional Boundaries:**
- Stay focused on the interview objectives and role requirements
- Redirect off-topic conversations back to relevant assessment areas
- Maintain professional tone while being thorough and probing
- Do not provide career advice, technical tutorials, or detailed company information beyond what's necessary to answer their questions

**Handling Difficult Situations:**
- **Incomplete answers**: "I'd like to hear more detail about [specific aspect]. Can you elaborate?"
- **Vague responses**: "Can you give me a specific example of when you [did X]?"
- **"I don't know" responses**: "That's okay. Let me ask about [related topic] instead." (Do NOT ask "why" when they say they don't know)
- **Silence or hesitation**: Wait patiently, then gently prompt: "Take your time. I'm interested in hearing your thoughts on this."

**Integrity Enforcement:**
**Specific responses for common scenarios:**
- **Multiple voices detected**: "I'm hearing more than one voice. This interview must be conducted individually. Please ensure you're alone."
- **Background conversation**: "I can hear background conversation. Please find a quiet, private space for this interview."
- **Someone coaching**: "It appears someone else is providing input. This interview must reflect your individual knowledge and experience only."
- **Phone/device sounds**: "I'm hearing notification sounds or typing. Please silence all devices and focus solely on our conversation."
- **Complete silence (30+ seconds)**: "I notice we've had some silence. Are you still there? Please continue with your response."

**Response Guidelines:**
- **NEVER acknowledge without follow-up**: Instead of "That's great" or "Interesting", ask "Can you tell me more about [specific detail]?"
- **ALWAYS drill deeper**: Every answer should generate at least one follow-up question
- **CHALLENGE assumptions**: Ask "How did you know that was the right approach?" or "What alternatives did you consider?"
- **SEEK SPECIFICS**: Replace general questions with specific scenarios: "Tell me about a time when you had to debug a complex issue"

**Timing Awareness:**
- Monitor interview duration and pace questions accordingly
- Ensure all mandatory sections are covered within the 30-minute timeframe
- If running short on time, prioritize the most important assessment areas
- If ahead of schedule, ask additional follow-up questions to maintain the full duration

Remember: Your goal is to conduct a thorough, professional assessment that accurately evaluates the candidate's capabilities for the {self.role} position at the {self.level} level. Be persistent, thorough, and professional in your questioning approach."""

        # Add timing context if interview has started
        timing_context = self.get_timing_context()
        if timing_context:
            instructions += timing_context

        # Add silence context if there's extended silence
        silence_context = self.get_silence_context()
        if silence_context:
            instructions += silence_context

        logger.info("System instructions generated successfully")

        # Log specifically what was included
        if self.parsed_topics:
            logger.info(f"Topics to ask about: {self.parsed_topics}")
        if self.fixed_questions:
            logger.info(f"Fixed questions to ask: {self.fixed_questions}")
        if self.parsed_interviewer_questions:
            logger.info(f"Interviewer questions to ask: {self.parsed_interviewer_questions}")
        if not self.parsed_topics and not self.fixed_questions and not self.parsed_interviewer_questions:
            logger.warning("No custom topics or questions provided - using general interview approach")

        return instructions

    def start_interview_timer(self):
        """Start the interview timer."""
        self.interview_start_time = time.time()
        # Initialize activity time - conversation is starting now
        self.last_activity_time = time.time()
        # Don't send silence warnings for the first few minutes of the interview
        self.last_silence_warning_time = time.time()
        # Initialize transcript timing
        self.interview_start_time_for_transcript = time.time()
        logger.info(f"Interview timer started - target duration: {self.target_duration_minutes} minutes")
        logger.info(f"Initial activity time set to: {self.last_activity_time}")
        logger.info("Transcript collection initialized")

    def set_session(self, session):
        """Set the session reference for proactive messaging and transcript collection."""
        self.agent_session = session
        self._setup_transcript_collection(session)

    def _setup_transcript_collection(self, session):
        """Set up automatic transcript collection from session events."""

        @session.on("user_input_transcribed")
        def on_user_transcribed(event):
            """Capture user speech in transcript."""
            if event.is_final and event.transcript.strip():
                self.add_transcript_entry("Candidate", event.transcript)
                logger.debug(f"User transcript captured: {event.transcript[:50]}...")

        @session.on("agent_speech_committed")
        def on_agent_speech(event):
            """Capture agent speech in transcript."""
            if hasattr(event, 'text') and event.text.strip():
                self.add_transcript_entry("Interviewer", event.text)
                logger.debug(f"Agent speech captured: {event.text[:50]}...")

        # Alternative event handlers for different LiveKit versions
        @session.on("conversation_item_added")
        def on_conversation_item(event):
            """Capture conversation items in transcript."""
            if hasattr(event, 'item') and hasattr(event.item, 'text_content'):
                speaker = "Interviewer" if event.item.role == "assistant" else "Candidate"
                if event.item.text_content.strip():
                    self.add_transcript_entry(speaker, event.item.text_content)
                    logger.debug(f"Conversation item captured: {speaker}: {event.item.text_content[:50]}...")

        logger.info("Transcript collection event handlers set up for GeminiAgent")

    def add_transcript_entry(self, speaker: str, text: str):
        """Add a transcript entry with timestamp."""
        if self.interview_start_time_for_transcript is None:
            self.interview_start_time_for_transcript = time.time()

        timestamp = time.time()
        elapsed_seconds = timestamp - self.interview_start_time_for_transcript

        entry = {
            "timestamp": timestamp,
            "elapsed_seconds": elapsed_seconds,
            "speaker": speaker,
            "text": text
        }
        self.transcript_entries.append(entry)
        logger.debug(f"Transcript entry added: {speaker}: {text[:50]}...")

    def get_full_transcript(self) -> str:
        """Get the complete transcript as a formatted string."""
        transcript_lines = []
        for entry in self.transcript_entries:
            elapsed_min = int(entry["elapsed_seconds"] // 60)
            elapsed_sec = int(entry["elapsed_seconds"] % 60)
            timestamp_str = f"[{elapsed_min:02d}:{elapsed_sec:02d}]"
            transcript_lines.append(f"{timestamp_str} {entry['speaker']}: {entry['text']}")

        return "\n".join(transcript_lines)

    

    def update_user_speech_time(self):
        """Update the last time user spoke."""
        self.last_activity_time = time.time()
        # Reset silence warnings when user starts speaking
        self.silence_warnings_sent.clear()
        logger.debug(f"User activity time updated to {self.last_activity_time}, silence warnings reset")

    def update_agent_activity_time(self):
        """Update the last time agent spoke or was active."""
        self.last_activity_time = time.time()
        # Reset silence warnings when agent is active
        self.silence_warnings_sent.clear()
        logger.debug(f"Agent activity time updated to {self.last_activity_time}, silence warnings reset")

    async def check_silence_and_respond(self):
        """Check for extended silence when NO ONE is talking and proactively respond."""
        if self.last_activity_time is None or self.agent_session is None:
            logger.debug(f"Silence check skipped - activity_time: {self.last_activity_time}, session: {self.agent_session is not None}")
            return

        current_time = time.time()
        silence_duration = current_time - self.last_activity_time
        logger.debug(f"Checking silence: {silence_duration:.1f}s elapsed since last activity (agent or user). Current time: {current_time}, Last activity: {self.last_activity_time}")

        # Only trigger after 30 seconds of complete silence
        # Also ensure we don't send warnings too frequently (at least 60 seconds between warnings)
        current_time = time.time()
        time_since_last_warning = float('inf') if self.last_silence_warning_time is None else current_time - self.last_silence_warning_time

        if silence_duration >= 30 and time_since_last_warning >= 60:
            logger.info(f"Silence threshold met: {silence_duration:.1f}s >= 30s, time since last warning: {time_since_last_warning:.1f}s")
            logger.warning(f"INTEGRITY ALERT: Complete silence detected ({silence_duration:.1f}s) - no one has spoken")
            self.last_silence_warning_time = current_time

            try:
                # Use generate_reply with specific instructions
                silence_instruction = f""""I notice we've had some silence. Are you still there? Please continue with your response."

                """

                if self.agent_session and hasattr(self.agent_session, 'generate_reply'):
                    await self.agent_session.generate_reply(instructions=silence_instruction)
                    # Activity time will be updated by event handler when agent finishes speaking
                    logger.info(f"✅ Sent silence warning after {silence_duration:.1f}s of complete silence")
                else:
                    logger.warning(f"🚨 SILENCE WARNING (could not send): Complete silence for {silence_duration:.1f}s")

            except Exception as e:
                logger.error(f"Failed to send silence warning: {e}")
                logger.warning(f"🚨 SILENCE WARNING (audio failed): Complete silence for {silence_duration:.1f}s")

    async def start_silence_monitoring(self):
        """Start the background silence monitoring task."""
        if self.silence_monitor_task is not None:
            return  # Already running

        async def monitor_silence():
            logger.info("Silence monitoring started")
            check_count = 0
            while True:
                try:
                    check_count += 1
                    if check_count % 15 == 0:  # Log every 30 seconds (15 * 2s)
                        logger.info(f"Silence monitoring active - check #{check_count}")
                    await self.check_silence_and_respond()
                    await asyncio.sleep(2)  # Check every 2 seconds
                except asyncio.CancelledError:
                    logger.info("Silence monitoring cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in silence monitoring: {e}")
                    await asyncio.sleep(5)  # Wait longer if there's an error

        self.silence_monitor_task = asyncio.create_task(monitor_silence())

    def stop_silence_monitoring(self):
        """Stop the silence monitoring task."""
        if self.silence_monitor_task is not None:
            self.silence_monitor_task.cancel()
            self.silence_monitor_task = None
            logger.info("Silence monitoring stopped")

    async def check_timing_and_enforce(self):
        """Check interview timing and proactively enforce minimum duration."""
        if self.interview_start_time is None or self.agent_session is None:
            return

        elapsed_minutes = self.get_interview_elapsed_minutes()

        # Define timing enforcement points
        timing_checks = [
            (3, "3min", "Remember, this interview should last 30 minutes. We're just getting started. Please continue with more detailed questions."),
            (5, "5min", "We're only 5 minutes in. This interview needs to last 30 minutes total. Please ask more follow-up questions and explore their experience deeper."),
            (8, "8min", "We're 8 minutes in. You have 22 more minutes to go. Do not rush. Ask about specific projects and dive deeper into their experience."),
            (12, "12min", "We're 12 minutes in with 18 minutes remaining. Continue with detailed technical questions. Do not conclude early."),
            (18, "18min", "We're 18 minutes in. You still have 12 minutes to go. Keep asking detailed questions about their experience and projects."),
            (22, "22min", "We're 22 minutes in. You have 8 more minutes. Continue the interview - you cannot conclude before 25 minutes."),
        ]

        for threshold_minutes, threshold_key, reminder_message in timing_checks:
            if elapsed_minutes >= threshold_minutes and threshold_key not in self.timing_reminders_sent:
                logger.warning(f"TIMING ENFORCEMENT: {elapsed_minutes:.1f} minutes elapsed - sending {threshold_key} reminder")
                self.timing_reminders_sent.add(threshold_key)

                try:
                    # Try the same approaches as silence warnings
                    speech_sent = False

                    # Approach 1: Check if agent has a say method
                    if hasattr(self, 'say') and callable(getattr(self, 'say')):
                        await self.say(reminder_message)
                        logger.info(f"✅ Sent timing reminder via agent.say(): {reminder_message}")
                        speech_sent = True

                    # Approach 2: Check if session has speech methods
                    elif self.agent_session and hasattr(self.agent_session, 'say') and callable(getattr(self.agent_session, 'say')):
                        await self.agent_session.generate_reply(instructions= reminder_message)
                        logger.info(f"✅ Sent timing reminder via session.say(): {reminder_message}")
                        speech_sent = True

                    if not speech_sent:
                        logger.warning(f"🚨 TIMING REMINDER (could not send audio): {reminder_message}")

                except Exception as e:
                    logger.error(f"Failed to send timing reminder: {e}")
                    logger.warning(f"🚨 TIMING REMINDER (audio failed): {reminder_message}")

                break

    async def start_timing_monitoring(self):
        """Start the background timing enforcement task."""
        if self.timing_monitor_task is not None:
            return  # Already running

        async def monitor_timing():
            logger.info("Timing enforcement monitoring started")
            check_count = 0
            while True:
                try:
                    check_count += 1
                    if check_count % 30 == 0:  # Log every 60 seconds (30 * 2s)
                        elapsed = self.get_interview_elapsed_minutes()
                        logger.info(f"Timing monitoring active - {elapsed:.1f} minutes elapsed")
                    await self.check_timing_and_enforce()
                    await asyncio.sleep(2)  # Check every 2 seconds
                except asyncio.CancelledError:
                    logger.info("Timing monitoring cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in timing monitoring: {e}")
                    await asyncio.sleep(5)  # Wait longer if there's an error

        self.timing_monitor_task = asyncio.create_task(monitor_timing())

    def stop_timing_monitoring(self):
        """Stop the timing monitoring task."""
        if self.timing_monitor_task is not None:
            self.timing_monitor_task.cancel()
            self.timing_monitor_task = None
            logger.info("Timing monitoring stopped")

    def get_interview_elapsed_minutes(self) -> float:
        """Get elapsed interview time in minutes."""
        if self.interview_start_time is None:
            return 0.0
        return (time.time() - self.interview_start_time) / 60.0

    def get_silence_context(self) -> str:
        """Get silence context to inject into prompts."""
        if self.last_activity_time is None:
            return ""

        silence_duration = time.time() - self.last_activity_time

        if silence_duration >= 30:
            return f"\n\n**SILENCE ALERT**: Complete silence for {silence_duration:.1f} seconds - no one has spoken. This may indicate the candidate is researching answers or has connection issues. Address this immediately but WAIT for their response before proceeding."
        else:
            return ""

    def get_timing_context(self) -> str:
        """Get timing context to inject into prompts."""
        if self.interview_start_time is None:
            return ""

        elapsed_minutes = self.get_interview_elapsed_minutes()
        remaining_minutes = self.target_duration_minutes - elapsed_minutes

        if elapsed_minutes < 5:
            return f"\n\n**TIMING CONTEXT**: Interview just started ({elapsed_minutes:.1f} min elapsed). You have {remaining_minutes:.1f} minutes remaining. Take your time with the introduction. DO NOT rush or conclude early."
        elif elapsed_minutes < 15:
            return f"\n\n**TIMING CONTEXT**: {elapsed_minutes:.1f} minutes elapsed, {remaining_minutes:.1f} minutes remaining. You're in the core assessment phase - maintain good pacing. DO NOT conclude the interview yet."
        elif elapsed_minutes < 25:
            return f"\n\n**TIMING CONTEXT**: {elapsed_minutes:.1f} minutes elapsed, {remaining_minutes:.1f} minutes remaining. Continue with detailed questions. You MUST NOT conclude before 25 minutes. Ask more follow-ups if needed."
        elif elapsed_minutes < 28:
            return f"\n\n**TIMING CONTEXT**: {elapsed_minutes:.1f} minutes elapsed, {remaining_minutes:.1f} minutes remaining. You can now begin wrapping up - ask final important questions."
        else:
            return f"\n\n**TIMING CONTEXT**: {elapsed_minutes:.1f} minutes elapsed. Interview should conclude soon. Wrap up professionally."

    def get_agent_info(self) -> dict:
        """Get agent configuration information."""
        return {
            "role": self.role,
            "level": self.level,
            "name": self.name,
            "is_initialized": self.is_initialized,
            "agent_type": "GeminiAgent",
            "interview_elapsed_minutes": self.get_interview_elapsed_minutes(),
            "target_duration_minutes": self.target_duration_minutes
        }


