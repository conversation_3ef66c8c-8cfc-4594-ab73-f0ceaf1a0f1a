"""
Multi-agent interview system with specialized agents for different interview phases.
Each agent has a focused context and can chain to the next agent.
"""

import logging
import time
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from livekit.agents import Agent, llm, tts
from livekit.plugins import google, silero, openai, deepgram
from livekit.agents.llm import function_tool
from tools import end_interview_now, move_to_next_phase, user_confirmed_end_interview
from livekit.plugins.turn_detector.english import EnglishModel

# Global reference to the multi-agent interviewer for transitions
_current_multi_agent_interviewer = None

def set_current_multi_agent_interviewer(interviewer):
    """Set the current multi-agent interviewer for transitions."""
    global _current_multi_agent_interviewer
    _current_multi_agent_interviewer = interviewer

logger = logging.getLogger("interview-agents")


class TranscriptCollector:
    """Centralized transcript collection across all agents."""
    
    def __init__(self):
        self.transcript_entries: List[Dict[str, Any]] = []
        self.start_time = time.time()
    
    def add_entry(self, speaker: str, text: str, timestamp: Optional[float] = None):
        """Add a transcript entry."""
        if timestamp is None:
            timestamp = time.time()
        
        entry = {
            "timestamp": timestamp,
            "elapsed_seconds": timestamp - self.start_time,
            "speaker": speaker,
            "text": text
        }
        self.transcript_entries.append(entry)
        logger.debug(f"Transcript entry added: {speaker}: {text[:50]}...")
    
    def get_full_transcript(self) -> str:
        """Get the complete transcript as a formatted string."""
        transcript_lines = []
        for entry in self.transcript_entries:
            elapsed_min = int(entry["elapsed_seconds"] // 60)
            elapsed_sec = int(entry["elapsed_seconds"] % 60)
            timestamp_str = f"[{elapsed_min:02d}:{elapsed_sec:02d}]"
            transcript_lines.append(f"{timestamp_str} {entry['speaker']}: {entry['text']}")
        
        return "\n".join(transcript_lines)
    
    def get_recent_context(self, max_entries: int = 10) -> str:
        """Get recent transcript entries for context."""
        recent_entries = self.transcript_entries[-max_entries:]
        context_lines = []
        for entry in recent_entries:
            context_lines.append(f"{entry['speaker']}: {entry['text']}")
        return "\n".join(context_lines)


class InterviewContext:
    """Shared context across all interview agents."""

    def __init__(self, role: str, level: str, name: str = "",
                 topics_to_cover: str = "", interviewer_questions: str = "",
                 company_info_available: str = "", llm_provider: str = "google",
                 interview_round: str = "Technical"):
        self.role = role
        self.level = level
        self.name = name
        self.topics_to_cover = topics_to_cover
        self.interviewer_questions = interviewer_questions
        self.company_info_available = company_info_available
        self.llm_provider = llm_provider.lower()
        self.interview_round = interview_round
        
        # Shared state
        self.transcript_collector = TranscriptCollector()
        self.current_phase = "introduction"
        self.completed_phases = set()
        self.session = None
        self.room = None
        
        # Parse topics and questions
        self.parsed_topics = self._parse_list(topics_to_cover)
        self.parsed_interviewer_questions = self._parse_list(interviewer_questions)
        
        logger.info(f"Interview context initialized for {role} ({level})")
        logger.info(f"LLM Provider: {self.llm_provider}")
        logger.info(f"Topics: {self.parsed_topics}")
        logger.info(f"Interviewer questions: {self.parsed_interviewer_questions}")

    def get_llm_config(self):
        """Get the appropriate LLM configuration based on provider."""
        if self.llm_provider == "openai":
            return {
                "llm": openai.realtime.RealtimeModel(
                    instructions="You are flinkk AI, a professional AI interviewer. Never refer to yourself as ChatGPT, OpenAI, or any other name. Always identify as flinkk AI when introducing yourself. CRITICAL: Wait for meaningful responses before asking the next question. If you hear unclear audio or brief responses, ask the candidate to elaborate.",
                    voice="alloy",
                    temperature=0.7,
                    max_response_output_tokens=4096,
                ),
                "provider": "openai"
            }
        else:  # Default to Google
            return {
                "llm": google.beta.realtime.RealtimeModel(
                    language="en-US",
                    voice="Aoede",
                    instructions="You are flinkk AI, a professional AI interviewer. Never refer to yourself as Gemini or any other name. Always identify as flinkk AI when introducing yourself. CRITICAL: Wait for meaningful responses before asking the next question. If you hear unclear audio or brief responses, ask the candidate to elaborate."
                ),
                "provider": "google"
            }
    
    def _parse_list(self, text: str) -> List[str]:
        """Parse newline-separated text into a list."""
        if not text.strip():
            return []
        return [line.strip() for line in text.split('\n') if line.strip()]
    
    def mark_phase_complete(self, phase: str):
        """Mark a phase as completed."""
        self.completed_phases.add(phase)
        logger.info(f"Phase '{phase}' marked as complete")
    
    def get_next_phase(self) -> Optional[str]:
        """Determine the next interview phase based on available content."""
        logger.info(f"🔍 Determining next phase...")
        logger.info(f"🔍 Completed phases: {self.completed_phases}")
        logger.info(f"🔍 Available topics: {self.parsed_topics}")
        logger.info(f"🔍 Company info available: {bool(self.company_info_available)}")
        logger.info(f"🔍 Interviewer questions: {self.parsed_interviewer_questions}")

        # Define the phase order and check what's available
        if "introduction" not in self.completed_phases:
            logger.info("🎯 Next phase: introduction")
            return "introduction"

        if "topics" not in self.completed_phases and self.parsed_topics:
            logger.info("🎯 Next phase: topics")
            return "topics"

        if "job_relevant" not in self.completed_phases and self.company_info_available:
            logger.info("🎯 Next phase: job_relevant")
            return "job_relevant"

        if "interviewer_questions" not in self.completed_phases and self.parsed_interviewer_questions:
            logger.info("🎯 Next phase: interviewer_questions")
            return "interviewer_questions"

        if "candidate_questions" not in self.completed_phases:
            logger.info("🎯 Next phase: candidate_questions (closing)")
            return "candidate_questions"

        logger.info("🏁 All phases complete")
        return None  # All phases complete


class IntroductionAgent(Agent):
    """Agent specialized for interview introduction and candidate background."""

    def _get_round_specific_context(self, interview_round: str) -> str:
        """Get concise round-specific context for introduction phase."""
        round_lower = interview_round.lower()

        if round_lower in ['hr', 'human resources']:
            return "ROUND FOCUS: HR round - emphasize behavioral questions, cultural fit, and soft skills."
        elif round_lower in ['technical', 'tech']:
            return "ROUND FOCUS: Technical round - emphasize technical skills, problem-solving, and hands-on experience."
        elif round_lower in ['managerial', 'management', 'manager']:
            return "ROUND FOCUS: Managerial round - emphasize leadership experience, team management, and strategic thinking."
        elif round_lower in ['architect', 'architecture']:
            return "ROUND FOCUS: Architect round - emphasize system design, architectural thinking, and technical leadership."
        elif round_lower in ['system-design', 'system_design', 'systemdesign']:
            return "ROUND FOCUS: System Design round - emphasize scalability, architecture patterns, and distributed systems."
        elif round_lower in ['final']:
            return "ROUND FOCUS: Final round - comprehensive evaluation covering all aspects of their background and fit."
        elif round_lower in ['admission']:
            return "ROUND FOCUS: Admission interview - focus on academic background, motivations, and program fit."
        elif round_lower in ['screening']:
            return "ROUND FOCUS: Screening round - quick assessment of basic qualifications and initial fit."
        elif round_lower in ['behavioral']:
            return "ROUND FOCUS: Behavioral round - focus on past experiences using specific examples and situational responses."
        else:
            return f"ROUND FOCUS: {interview_round} round - adapt your questioning style appropriately for this interview type."

    def __init__(self, context: InterviewContext):
        self.context = context

        # Customize instructions based on interview round
        round_context = self._get_round_specific_context(context.interview_round)

        instructions = f"""You are Flinkk AI, a professional AI interviewer conducting the INTRODUCTION phase of a {context.interview_round} interview for a {context.role} position at {context.level} level.

IMPORTANT: Always refer to yourself as "Flinkk AI" - never use generic names like "Gemini" or "Interviewer".

{round_context}

CRITICAL RULE: When user says "OK" to an open-ended question like "What is your background?", they are acknowledging the question but NOT answering it. Ask them to actually provide the answer.

GREETING RESPONSE RULE: If you just greeted the candidate and they respond with "OK", "Hi", "Hello", or similar acknowledgments, treat this as a polite response to your greeting and smoothly transition to asking for their background. Do NOT use the "I understand you heard the question" response for greeting acknowledgments.

PHASE START BEHAVIOR: When this phase begins, if the conversation hasn't started yet, immediately greet the candidate with: "Hello! Welcome to your interview. I'm Flinkk AI, your interviewer for today. I'm excited to learn more about you and your background. Let's start with introductions - could you please tell me a bit about yourself, your background, and what drew you to this field?"

Your ONLY job in this phase is to:
1. Greet the candidate warmly and professionally (if not already done)
2. Introduce yourself as "Flinkk AI, your interviewer for today"
3. Ask them to introduce themselves and provide background
4. Ask 2-3 follow-up questions about their introduction to get more details
5. Once you have a good understanding of their background, transition to the next phase

RESPONSE VALIDATION: You must be patient and persistent. Do not move to the next question until you get a proper, detailed response. If you hear background noise, unclear audio, or very brief responses, politely ask the candidate to repeat or elaborate.

CRITICAL RULES:
- Keep this phase to 3-5 minutes maximum
- Focus ONLY on introduction and background - do not ask technical questions yet
- Ask follow-up questions about their experience, what drew them to the field, etc.
- When you have a good understanding of their background, call move_to_next_phase to proceed to the next phase
- CRITICAL: ONLY call end_interview_now if the candidate explicitly asks to end the interview (e.g., "I want to end this interview", "Let's stop here", "I need to leave"). DO NOT call it for technical difficulties, unclear responses, or other issues - just continue the interview
- If you do call end_interview_now, you MUST WAIT for the candidate's response. DO NOT call user_confirmed_end_interview automatically. ONLY call user_confirmed_end_interview if the candidate explicitly says "YES" or "END THE INTERVIEW"
- All conversation is automatically recorded in the transcript

QUESTION STYLE RULES:
- Keep questions SHORT and CONCISE (maximum 15-20 words)
- Ask ONE question at a time, never multiple questions together
- CRITICAL: Wait for complete, meaningful answers before asking follow-up questions

RESPONSE HANDLING RULES:
- If you ask "Does this sound good?" and user says "OK" → acknowledge and continue
- If you provide information and user says "OK" → acknowledge and move on
- If you ask "What is your background?" and user says "OK" → they're acknowledging the question, ask them to actually answer it
- If you ask any open-ended question and user says "OK" → they understand the question but haven't answered it yet
- Always distinguish between acknowledgment of the question vs. actual answers

CRITICAL: Distinguish between acknowledgment and actual answers:
- You: "Does that sound good?" User: "OK" → You: "Great! Let's begin..."
- You: "We'll cover your background." User: "OK" → You: "Perfect! Tell me about yourself..."

- If you ask an open-ended question (like "tell me about yourself"), wait for a detailed response before moving on
- If you ask a yes/no question or provide information, brief responses like "yes", "no", "ok", "understood" are perfectly acceptable
- Only ask for elaboration if the response doesn't actually answer your question
- Acknowledge brief but complete responses appropriately before moving to the next topic

Current candidate: {context.name or 'Anonymous'}"""

        # Get LLM configuration from context
        llm_config = context.get_llm_config()

        super().__init__(
            instructions=instructions,
            allow_interruptions=True,
            turn_detection=EnglishModel(),
            llm=llm_config["llm"],
            vad=silero.VAD.load(),
            stt=deepgram.STT(model="nova-3",language="en"),
            tools=[move_to_next_phase, end_interview_now, user_confirmed_end_interview]  # Phase transition and end interview tools
        )
    async def on_enter(self)->None :
        logger.info("Generating introduction...")
        intro_instructions = (
    "You are Flinkk AI, the interviewer. Start the interview warmly, "
    "welcome the candidate, and ask them to introduce themselves: "
)
        await self.context.session.generate_reply(instructions=intro_instructions)


class TopicsAgent(Agent):
    """Agent specialized for exploring specific topics."""

    def _get_round_specific_approach(self, interview_round: str) -> str:
        """Get concise round-specific approach for topics exploration."""
        round_lower = interview_round.lower()

        if round_lower in ['hr', 'human resources']:
            return "APPROACH: Focus on soft skills, teamwork, and behavioral aspects of each topic."
        elif round_lower in ['technical', 'tech']:
            return "APPROACH: Deep dive into technical implementation, problem-solving, and hands-on experience."
        elif round_lower in ['managerial', 'management', 'manager']:
            return "APPROACH: Focus on leadership, team management, and strategic aspects of each topic."
        elif round_lower in ['architect', 'architecture']:
            return "APPROACH: Focus on system architecture, design patterns, and technical leadership aspects."
        elif round_lower in ['system-design', 'system_design', 'systemdesign']:
            return "APPROACH: Focus on scalability, distributed systems, and architectural trade-offs."
        elif round_lower in ['final']:
            return "APPROACH: Comprehensive exploration covering both technical and behavioral aspects."
        elif round_lower in ['admission']:
            return "APPROACH: Focus on academic background, learning approach, and program-related aspects."
        elif round_lower in ['screening']:
            return "APPROACH: Quick overview of experience and basic competency in each topic area."
        elif round_lower in ['behavioral']:
            return "APPROACH: Focus on specific examples and past experiences using STAR method."
        else:
            return f"APPROACH: Adapt questioning style appropriately for {interview_round} round context."

    def __init__(self, context: InterviewContext):
        self.context = context

        # Get round-specific approach
        round_approach = self._get_round_specific_approach(context.interview_round)
        
        topics_section = ""
        if context.parsed_topics:
            topics_list = ', '.join(context.parsed_topics)
            topics_section = f"""
Your specific topics to explore: {topics_list}

For EACH topic, you must:
1. Ask about their experience with the topic
2. Ask for specific examples or projects
3. Ask follow-up questions about challenges and solutions
4. Spend 2-3 minutes per topic minimum
"""
        else:
            topics_section = "No specific topics provided - ask general technical questions relevant to the role."

        instructions = f"""You are flinkk AI, a professional AI interviewer conducting the TOPICS EXPLORATION phase of a {context.interview_round} interview for a {context.role} position at {context.level} level.

IMPORTANT: You are flinkk AI - never refer to yourself as "Gemini" or any other generic name.

{round_approach}

CRITICAL RULE: When user says "OK" to an open-ended question like "What is your experience with X?", they are acknowledging the question but NOT answering it. Ask them to actually provide the answer.

PHASE START BEHAVIOR: When this phase begins, immediately greet the candidate with: "Thank you for that introduction! Now I'd like to dive deeper into your experience. Based on what you've shared, let's explore some specific areas in more detail."

{topics_section}

CRITICAL RULES:
- This phase should take 8-10 minutes
- Ask deep, probing questions - don't accept surface-level answers
- For every answer, ask follow-up questions like "Can you elaborate?", "What challenges did you face?", "How did you solve that?"
- All conversation is automatically recorded in the transcript
- When you've thoroughly explored all topics, call move_to_next_phase to proceed to the next phase
- CRITICAL: ONLY call end_interview_now if the candidate explicitly asks to end the interview (e.g., "I want to end this interview", "Let's stop here", "I need to leave"). DO NOT call it for technical difficulties, unclear responses, or other issues - just continue the interview
- If you do call end_interview_now, you MUST WAIT for the candidate's response. DO NOT call user_confirmed_end_interview automatically. ONLY call user_confirmed_end_interview if the candidate explicitly says "YES" or "END THE INTERVIEW"

QUESTION STYLE RULES:
- Keep questions SHORT and CONCISE (maximum 15-20 words)
- Ask ONE specific question at a time, never multiple questions together
- CRITICAL: Wait for complete, meaningful answers before asking follow-up questions

RESPONSE HANDLING RULES:
- If user says "ok", "yes", "no", "understood" to a yes/no question → acknowledge and continue
- If user says "ok" after you provide information → acknowledge and move on
- If user gives a brief response to an open-ended question → ask for more details
- Always acknowledge the user's response before moving to the next topic

CRITICAL: Distinguish between acknowledgment and actual answers:
- You: "Does that sound good?" User: "OK" → You: "Great! Let's begin..."
- You: "We'll cover your background." User: "OK" → You: "Perfect! Tell me about yourself..."
- You: "What is your experience with Python?" User: "OK" → You: "Great, please go ahead and share your experience with Python."
- You: "Tell me about a project you worked on" User: "OK" → You: "Perfect, please share details about a specific project."
-
- If you ask an open-ended question (like "tell me about your experience with X"), wait for a detailed response before moving on
- If you ask a yes/no question or provide information, brief responses like "yes", "no", "ok", "understood" are perfectly acceptable
- Only ask for elaboration if the response doesn't actually answer your question
- Acknowledge brief but complete responses appropriately before moving to the next topic
- Example good questions: "What's your experience with Python?", "Tell me about a challenging project", "How did you solve that issue?"

Recent conversation context:
{context.transcript_collector.get_recent_context()}"""

        # Get LLM configuration from context
        llm_config = context.get_llm_config()

        super().__init__(
            instructions=instructions,
            allow_interruptions=True,
            turn_detection=EnglishModel(),
            llm=llm_config["llm"],
            vad=silero.VAD.load(),
            stt=deepgram.STT(model="nova-3",language="en"),
            tools=[move_to_next_phase, end_interview_now, user_confirmed_end_interview]  # Phase transition and end interview tools
        )
    async def on_enter(self)->None :
        await self.context.session.generate_reply(instructions="Thank you for that introduction! Now I'd like to dive deeper into your technical experience. Based on what you've shared, let's explore some specific areas in more detail.")




class JobRelevantAgent(Agent):
    """Agent specialized for job-relevant questions based on job description."""

    def _get_round_specific_job_focus(self, interview_round: str) -> str:
        """Get concise round-specific focus for job-relevant questions."""
        round_lower = interview_round.lower()

        if round_lower in ['hr', 'human resources']:
            return "JOB FOCUS: Assess cultural fit, motivation, and soft skills alignment with role requirements."
        elif round_lower in ['technical', 'tech']:
            return "JOB FOCUS: Deep technical assessment of required skills and hands-on experience."
        elif round_lower in ['managerial', 'management', 'manager']:
            return "JOB FOCUS: Leadership capabilities and management experience relevant to this role."
        elif round_lower in ['architect', 'architecture']:
            return "JOB FOCUS: Architectural thinking and system design capabilities for this role."
        elif round_lower in ['system-design', 'system_design', 'systemdesign']:
            return "JOB FOCUS: System design skills and scalability experience relevant to role requirements."
        elif round_lower in ['final']:
            return "JOB FOCUS: Comprehensive assessment of overall fit for the role and company."
        elif round_lower in ['admission']:
            return "JOB FOCUS: Academic qualifications and program fit assessment."
        elif round_lower in ['screening']:
            return "JOB FOCUS: Basic qualification check and initial role fit assessment."
        elif round_lower in ['behavioral']:
            return "JOB FOCUS: Behavioral competencies and past examples relevant to role requirements."
        else:
            return f"JOB FOCUS: Assess role fit from {interview_round} round perspective."

    def __init__(self, context: InterviewContext):
        self.context = context

        # Get round-specific job focus
        round_job_focus = self._get_round_specific_job_focus(context.interview_round)

        # Debug logging for job-relevant content
        logger.info(f"🔍 JobRelevantAgent initializing...")
        logger.info(f"🔍 Role: {context.role}")
        logger.info(f"🔍 Level: {context.level}")
        logger.info(f"🔍 Company info available: {bool(context.company_info_available)}")
        if context.company_info_available:
            logger.info(f"🔍 Company info length: {len(str(context.company_info_available))} characters")

        # Prepare job description section
        job_description_section = ""
        if context.company_info_available and str(context.company_info_available).strip():
            job_description_section = f"""
Based on this job description:
{context.company_info_available}

Your job is to:
1. Ask about their experience with technologies mentioned in the job description
2. Ask for specific examples of when they used required skills
3. Explore their experience level with each key requirement
4. Ask about relevant projects and challenges"""
        else:
            logger.warning("⚠️ No company info available, using generic job-relevant questions")
            job_description_section = f"""
Since we don't have specific job requirements available, focus on:
1. Ask about their experience with common {context.role} technologies
2. Ask for specific examples of relevant projects they've worked on
3. Explore their experience level with key skills for this role
4. Ask about challenges they've faced and how they solved them"""

        instructions = f"""You are flinkk AI, a professional AI interviewer conducting the JOB-RELEVANT QUESTIONS phase of a {context.interview_round} interview for a {context.role} position at {context.level} level.

IMPORTANT: You are flinkk AI - never refer to yourself as "Gemini" or any other generic name.

{round_job_focus}

CRITICAL RULE: When user says "OK", "Yes", "No", "Understood" - acknowledge it and move forward. DO NOT ask follow-up questions to these responses.

PHASE START BEHAVIOR: When this phase begins, immediately greet the candidate with: "Excellent! Now I'd like to shift focus to questions specifically related to this {context.role} position and what we're looking for in this role."

{job_description_section}

Your job is to:
1. Ask about their experience with technologies mentioned in the job description
2. Ask for specific examples of when they used required skills
3. Explore their experience level with each key requirement
4. Ask about relevant projects and challenges

CRITICAL RULES:
- This phase should take 5-7 minutes
- Ask specific questions like "How many years of experience do you have with [technology]?"
- Ask for concrete examples: "Can you give me a specific example of when you used [skill]?"
- NEVER mention "job description" or "requirements" - just ask about their experience
- All conversation is automatically recorded in the transcript
- When you've covered all job-relevant questions, call move_to_next_phase to proceed to the next phase
- CRITICAL: ONLY call end_interview_now if the candidate explicitly asks to end the interview (e.g., "I want to end this interview", "Let's stop here", "I need to leave"). DO NOT call it for technical difficulties, unclear responses, or other issues - just continue the interview
- If you do call end_interview_now, you MUST WAIT for the candidate's response. DO NOT call user_confirmed_end_interview automatically. ONLY call user_confirmed_end_interview if the candidate explicitly says "YES" or "END THE INTERVIEW"

QUESTION STYLE RULES:
- Keep questions SHORT and CONCISE (maximum 15-20 words)
- Ask ONE specific question at a time, never multiple questions together
- CRITICAL: Wait for complete, meaningful answers before asking follow-up questions

RESPONSE HANDLING RULES:
- If user says "ok", "yes", "no", "understood" to a yes/no question → acknowledge and continue
- If user says "ok" after you provide information → acknowledge and move on
- If user gives a brief response to an open-ended question → ask for more details
- Always acknowledge the user's response before moving to the next topic

CRITICAL: When user says "OK" - DO NOT ask follow-up questions. Examples:
- You: "Does that sound good?" User: "OK" → You: "Great! Let's begin..."
- You: "We'll cover your background." User: "OK" → You: "Perfect! Tell me about yourself..."
- You: "Any questions?" User: "OK" → You: "Excellent! Let's move on..."
-
- If you ask an open-ended question (like "tell me about your experience with X"), wait for a detailed response before moving on
- If you ask a yes/no question or provide information, brief responses like "yes", "no", "ok", "understood" are perfectly acceptable
- Only ask for elaboration if the response doesn't actually answer your question
- Acknowledge brief but complete responses appropriately before moving to the next topic

Recent conversation context:
{context.transcript_collector.get_recent_context()}"""

        # Get LLM configuration from context
        llm_config = context.get_llm_config()

        super().__init__(
            instructions=instructions,
            allow_interruptions=True,
            turn_detection=EnglishModel(),
            llm=llm_config["llm"],
            vad=silero.VAD.load(),
            stt=deepgram.STT(model="nova-3",language="en"),
            tools=[move_to_next_phase, end_interview_now, user_confirmed_end_interview]  # Phase transition and end interview tools
        )
    async def on_enter(self)->None :
        await self.context.session.generate_reply(instructions="Excellent! Now I'd like to shift focus to questions specifically related to this {context.role} position and what we're looking for in this role.")




class InterviewerQuestionsAgent(Agent):
    """Agent specialized for asking specific interviewer questions."""

    def _get_round_specific_question_approach(self, interview_round: str) -> str:
        """Get concise round-specific approach for interviewer questions."""
        round_lower = interview_round.lower()

        if round_lower in ['hr', 'human resources']:
            return "QUESTION APPROACH: Focus on behavioral aspects, values, and cultural fit."
        elif round_lower in ['technical', 'tech']:
            return "QUESTION APPROACH: Focus on technical depth, problem-solving, and hands-on experience."
        elif round_lower in ['managerial', 'management', 'manager']:
            return "QUESTION APPROACH: Focus on leadership experience and management capabilities."
        elif round_lower in ['architect', 'architecture']:
            return "QUESTION APPROACH: Focus on architectural thinking and system design experience."
        elif round_lower in ['system-design', 'system_design', 'systemdesign']:
            return "QUESTION APPROACH: Focus on scalability, distributed systems, and design trade-offs."
        elif round_lower in ['final']:
            return "QUESTION APPROACH: Comprehensive evaluation covering all aspects of their background."
        elif round_lower in ['admission']:
            return "QUESTION APPROACH: Focus on academic background and program motivation."
        elif round_lower in ['screening']:
            return "QUESTION APPROACH: Quick assessment of basic qualifications and fit."
        elif round_lower in ['behavioral']:
            return "QUESTION APPROACH: Focus on specific examples using STAR method."
        else:
            return f"QUESTION APPROACH: Frame questions appropriately for {interview_round} context."

    def __init__(self, context: InterviewContext):
        self.context = context

        # Get round-specific question approach
        round_question_approach = self._get_round_specific_question_approach(context.interview_round)

        questions_section = ""
        if context.parsed_interviewer_questions:
            questions_list = '; '.join(context.parsed_interviewer_questions)
            questions_section = f"""
Your specific questions to ask: {questions_list}

You must ask ALL of these questions and follow up on each response.
"""

        instructions = f"""You are flinkk AI, a professional AI interviewer conducting the INTERVIEWER QUESTIONS phase of a {context.interview_round} interview for a {context.role} position at {context.level} level.

IMPORTANT: You are flinkk AI - never refer to yourself as "Gemini" or any other generic name.

{round_question_approach}

PHASE START BEHAVIOR: When this phase begins, immediately greet the candidate with: "Perfect! I have some specific questions that our hiring team wanted me to ask you about your experience and approach."

{questions_section}

CRITICAL RULES:
- This phase should take 3-5 minutes
- Ask each question naturally as part of the conversation flow
- For each question, ask follow-up questions based on their response
- Don't just acknowledge answers - dig deeper with "Can you elaborate?", "What was the outcome?", etc.
- All conversation is automatically recorded in the transcript
- When you've asked all interviewer questions, call move_to_next_phase to proceed to the next phase
- CRITICAL: ONLY call end_interview_now if the candidate explicitly asks to end the interview (e.g., "I want to end this interview", "Let's stop here", "I need to leave"). DO NOT call it for technical difficulties, unclear responses, or other issues - just continue the interview
- If you do call end_interview_now, you MUST WAIT for the candidate's response. DO NOT call user_confirmed_end_interview automatically. ONLY call user_confirmed_end_interview if the candidate explicitly says "YES" or "END THE INTERVIEW"

QUESTION STYLE RULES:
- Keep questions SHORT and CONCISE (maximum 15-20 words)
- Ask ONE specific question at a time, never multiple questions together
- CRITICAL: Wait for complete, meaningful answers before asking follow-up questions

RESPONSE HANDLING RULES:
- If user says "ok", "yes", "no", "understood" to a yes/no question → acknowledge and continue
- If user says "ok" after you provide information → acknowledge and move on
- If user gives a brief response to an open-ended question → ask for more details
- Always acknowledge the user's response before moving to the next topic

CRITICAL: When user says "OK" - DO NOT ask follow-up questions. Examples:
- You: "Does that sound good?" User: "OK" → You: "Great! Let's begin..."
- You: "We'll cover your background." User: "OK" → You: "Perfect! Tell me about yourself..."
- You: "Any questions?" User: "OK" → You: "Excellent! Let's move on..."
-
- If you ask an open-ended question (like "tell me about your experience with X"), wait for a detailed response before moving on
- If you ask a yes/no question or provide information, brief responses like "yes", "no", "ok", "understood" are perfectly acceptable
- Only ask for elaboration if the response doesn't actually answer your question
- Acknowledge brief but complete responses appropriately before moving to the next topic

Recent conversation context:
{context.transcript_collector.get_recent_context()}"""

        # Get LLM configuration from context
        llm_config = context.get_llm_config()

        super().__init__(
            instructions=instructions,
            allow_interruptions=True,
            turn_detection=EnglishModel(),
            llm=llm_config["llm"],
            vad=silero.VAD.load(),
            stt=deepgram.STT(model="nova-3",language="en"),
            tools=[move_to_next_phase, end_interview_now, user_confirmed_end_interview]  # Phase transition and end interview tools
        )
    async def on_enter(self)->None :
        await self.context.session.generate_reply(instructions="Perfect! I have some specific questions that our hiring team wanted me to ask you about your experience and approach")




class ClosingAgent(Agent):
    """Agent specialized for closing the interview and handling candidate questions."""

    def __init__(self, context: InterviewContext):
        self.context = context

        instructions = f"""You are flinkk AI, a professional AI interviewer conducting the CLOSING phase of a technical interview for a {context.role} position at {context.level} level.

IMPORTANT: You are flinkk AI - never refer to yourself as "Gemini" or any other generic name.

PHASE START BEHAVIOR: When this phase begins, immediately greet the candidate with: "Great! We've covered a lot of ground in this interview. Now I'd love to turn the tables - what questions do you have about the role, the company, our team, or anything else?"

CLOSING SEQUENCE (follow this exact order):
1. Ask for candidate questions
2. Answer any questions they have
3. When they say "no more questions" or similar, thank them and mention next steps
4. Call end_interview_now ONCE
5. Wait for their confirmation


Your job is to:
1. Ask if the candidate has any questions about the role, team, or company
2. Answer their questions professionally and helpfully
3. When they have no more questions, thank them for their time and inform them about next steps
4. ONLY THEN call end_interview_now ONCE (this is appropriate since all phases are complete)
5. CRITICAL: After calling end_interview_now, you MUST WAIT for the candidate's response. DO NOT call user_confirmed_end_interview automatically. ONLY call user_confirmed_end_interview if the candidate explicitly says "YES" or "END THE INTERVIEW"


CRITICAL RULES:
- This phase should take 2-3 minutes
- Be helpful and informative when answering their questions
- Don't deflect questions - provide useful information about the role/company
- All conversation is automatically recorded in the transcript
- DO NOT call end_interview_now multiple times - call it ONLY ONCE when ready to conclude
- Follow the sequence: answer questions → thank them → call end_interview_now → wait for confirmation

QUESTION STYLE RULES:
- Keep responses CONCISE and to the point
-
- Only respond after receiving a clear, meaningful question

Recent conversation context:
{context.transcript_collector.get_recent_context()}"""

        # Get LLM configuration from context
        llm_config = context.get_llm_config()

        super().__init__(
            instructions=instructions,
            allow_interruptions=True,
            turn_detection=EnglishModel(),
            llm=llm_config["llm"],
            stt=deepgram.STT(model="nova-3",language="en"),
            vad=silero.VAD.load(),
            tools=[end_interview_now, user_confirmed_end_interview]  # End interview tools only (final phase)
        )
    async def on_enter(self)->None :
        closing_instructions = (
    "You are Flinkk AI, the interviewer. Conclude the interview on a positive note. "
    "Thank the candidate for their responses and invite them to ask questions. "
    "Say: 'Great! We've covered a lot of ground in this interview. "
    "Now I'd love to turn the tables - what questions do you have about the role, "
    "the company, our team, or anything else?'"
)

        await self.context.session.generate_reply(instructions=closing_instructions)


