# =============================================================================
# LiveKit Configuration (Required)
# =============================================================================

# LiveKit Server Configuration
LIVEKIT_API_KEY=<your_api_key>
LIVEKIT_API_SECRET=<your_api_secret>
LIVEKIT_URL=wss://<project-subdomain>.livekit.cloud

# For manage_egress.py script (uses LIVEKIT_API_URL instead of LIVEKIT_URL)
LIVEKIT_API_URL=wss://<project-subdomain>.livekit.cloud

# =============================================================================
# Google Cloud Platform (Required)
# =============================================================================

# GCP Storage Bucket for Recordings
GCP_BUCKET=your-gcp-bucket-name


****************************************


