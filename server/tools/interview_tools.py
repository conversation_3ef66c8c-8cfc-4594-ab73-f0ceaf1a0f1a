import logging
import os
import json
from datetime import datetime
from livekit.agents import function_tool,RunContext,get_job_context

logger = logging.getLogger("interview-tools")


@function_tool()
async def end_interview_now(context:RunContext, reason: str = "Interview completed") -> str:
    """Request to end the interview - asks for confirmation and waits for user response.
    CRITICAL: ONLY call this function when:
    1. The candidate explicitly asks to end the interview, OR
    2. All interview phases are complete (only for ClosingAgent)
    DO NOT call for technical difficulties, unclear responses, or other issues."""
    logger.info(f"🤔 Interview end requested: {reason} - asking for confirmation")

    return f"""I understand you want to end the interview. Before I do that, let me confirm with you:

Please respond clearly:
- Say "YES, END THE INTERVIEW" if you want to end it now
- Say "NO, CONTINUE" if you want to keep going with the interview

IMPORTANT: I will wait for your clear response before taking any action. I will NOT end the interview until you explicitly confirm. What would you like to do?"""


@function_tool()
async def move_to_next_phase(context: RunContext, reason: str = "Current phase complete") -> str:
    """Move to the next interview phase when the current phase is complete."""
    try:
        logger.info(f"🔄 Phase transition requested: {reason}")

        # Get the multi-agent interviewer from the global context
        from agents.interview_agents import _current_multi_agent_interviewer

        if _current_multi_agent_interviewer:
            await _current_multi_agent_interviewer.trigger_phase_transition(reason)
            return f"✅ Successfully moved to next phase: {reason}"
        else:
            logger.error("❌ No multi-agent interviewer available for transition")
            return "❌ Cannot transition: No interviewer available"

    except Exception as e:
        logger.error(f"❌ Error transitioning to next phase: {e}", exc_info=True)
        return f"Error transitioning to next phase: {str(e)}"


@function_tool()
async def user_confirmed_end_interview(context:RunContext, reason: str = "Interview completed") -> str:
    """CRITICAL: ONLY call this function AFTER the user explicitly says YES to ending the interview.
    DO NOT call this function automatically. DO NOT call this immediately after end_interview_now.
    ONLY call this when the user clearly confirms they want to end the interview."""
    try:
        logger.info(f"🏁 Ending interview with confirmation: {reason}")

        room = get_job_context().room
        logger.info(f"🔍 Found room: {room.name}")

        # Get remote participants
        participants = room.remote_participants
        if not participants:
            logger.warning("⚠️ No participants connected")
            return "Warning: No participants connected, but interview ended"

        # Get the first participant
        participant = next(iter(participants.values()))
        if not participant:
            logger.warning("⚠️ No valid participant found")
            return "Warning: No valid participant, but interview ended"

        # Prepare RPC payload following LiveKit examples pattern
        rpc_payload = {
            "reason": reason,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "source": "ai_agent"
        }
        payload_str = json.dumps(rpc_payload)
        logger.info(f"📡 Sending END_INTERVIEW RPC to {participant.identity}: {payload_str}")

        # Send RPC following LiveKit examples pattern
        await room.local_participant.perform_rpc(
            destination_identity=participant.identity,
            method="client.endInterview",
            payload=payload_str,
            response_timeout=5.0
        )

        logger.info("✅ End interview RPC sent successfully")
        return f"Interview ended successfully: {reason}"

    except Exception as e:
        logger.error(f"❌ RPC call error: {e}", exc_info=True)
        return f"Error during RPC: {e}"