"""
Event handlers for LiveKit agent sessions.
Following LiveKit best practices for event-driven architecture.
"""

import logging
import asyncio
import time
from livekit.agents import (
    AgentSession,
    UserInputTranscribedEvent,
    ConversationItemAddedEvent,
    AgentStateChangedEvent,
    UserStateChangedEvent,
    ErrorEvent,
    CloseEvent,
)

logger = logging.getLogger("event-handlers")

# Global variables to track silence periods
last_user_speech_time = None
silence_warnings_sent = set()  # Track which warnings we've already sent

def check_silence_and_alert(session: AgentSession):
    """Check for extended silence and send appropriate alerts."""
    global last_user_speech_time, silence_warnings_sent

    if last_user_speech_time is None:
        return

    silence_duration = time.time() - last_user_speech_time

   

  


async def silence_monitor_task(session: AgentSession):
    """Background task to monitor for extended silence periods."""
    while True:
        try:
            check_silence_and_alert(session)
            await asyncio.sleep(2)  # Check every 2 seconds
        except Exception as e:
            logger.error(f"Error in silence monitor: {e}")
            await asyncio.sleep(5)  # Wait longer if there's an error

def setup_event_handlers(session: AgentSession):
    """Setup event handlers following LiveKit best practices."""

    @session.on("user_input_transcribed")
    def on_user_input_transcribed(event: UserInputTranscribedEvent):
        """Handle user input transcription events."""
        logger.info(f"User transcription: {event.transcript} (final: {event.is_final})")
        if event.speaker_id:
            logger.debug(f"Speaker ID: {event.speaker_id}")



    @session.on("conversation_item_added")
    def on_conversation_item_added(event: ConversationItemAddedEvent):
        """Handle conversation item addition events."""
        logger.info(f"Conversation item from {event.item.role}: {event.item.text_content}")
        if event.item.interrupted:
            logger.debug("Item was interrupted")



    @session.on("agent_state_changed")
    def on_agent_state_changed(event: AgentStateChangedEvent):
        """Handle agent state change events."""
        logger.info(f"Agent state: {event.old_state} -> {event.new_state}")



        # Log specific state transitions and update activity tracking
        if event.new_state == "listening":
            logger.debug("Agent is now listening for user input")
        elif event.new_state == "thinking":
            logger.debug("Agent is processing user input")
        elif event.new_state == "speaking":
            logger.debug("Agent is generating response")
        elif event.old_state == "speaking":
            logger.debug("Agent finished speaking")
            # Update agent activity time when agent finishes speaking
            if hasattr(session, 'agent') and hasattr(session.agent, 'update_agent_activity_time'):
                session.agent.update_agent_activity_time()
                logger.debug("Agent activity time updated - agent finished speaking")

    @session.on("user_state_changed")
    def on_user_state_changed(event: UserStateChangedEvent):
        """Handle user state change events."""
        logger.info(f"User state: {event.old_state} -> {event.new_state}")

        # Track user speech timing for integrity monitoring via agent
        if event.new_state == "speaking":
            logger.info("User started speaking - updating speech time")
            # Update the agent's speech tracking
            if hasattr(session, 'agent') and hasattr(session.agent, 'update_user_speech_time'):
                session.agent.update_user_speech_time()
                logger.info("Agent speech time updated successfully")
            else:
                logger.warning("Could not update agent speech time - agent or method not found")
        elif event.new_state == "listening":
            logger.info("User stopped speaking - silence monitoring will now track this")
    
    @session.on("error")
    def on_error(event: ErrorEvent):
        """Handle error events with appropriate logging level."""
        if event.error.recoverable:
            logger.warning(f"Recoverable error from {event.source}: {event.error}")
            logger.debug(f"Model config: {event.model_config}")
        else:
            logger.error(f"Unrecoverable error from {event.source}: {event.error}")
            logger.error(f"Model config: {event.model_config}")



    @session.on("close")
    def on_close(event: CloseEvent):
        """Handle session close events."""
        if event.error:
            logger.error(f"Session closed due to error: {event.error}")
        else:
            logger.info("Session closed normally")

    @session.on("speech_created")
    def on_speech_created(event):
        """Handle speech creation events."""
        logger.debug(f"Speech created - Source: {event.source}, User initiated: {event.user_initiated}")

    @session.on("function_tools_executed")
    def on_function_tools_executed(event):
        """Handle function tool execution events."""
        logger.info(f"Function tools executed: {len(event.function_calls)} calls")
        for call, output in event.zipped():
            logger.debug(f"Function {call.name} -> {output}")



    @session.on("metrics_collected")
    def on_metrics_collected(event):
        """Handle metrics collection events."""
        logger.debug(f"Metrics collected: {type(event.metrics).__name__}")


    logger.info("Event handlers setup completed")
