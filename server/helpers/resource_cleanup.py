"""
Resource cleanup utilities for LiveKit agent sessions.
Handles proper cleanup order and error handling.
"""

import logging
from typing import Optional
from livekit.agents import AgentSession
from storage import GCPEgressManager

logger = logging.getLogger("resource-cleanup")


async def cleanup_resources(
    session: Optional[AgentSession] = None, 
    gcp_manager: Optional[GCPEgressManager] = None, 
    egress_id: Optional[str] = None
):
    """
    Clean up resources following LiveKit best practices.
    
    Args:
        session: AgentSession to cleanup (optional)
        gcp_manager: GCP egress manager instance (optional)
        egress_id: Egress ID to stop recording (optional)
    """
    logger.info("Starting resource cleanup...")
    
    # Step 1: Stop recording first to preserve data
    if gcp_manager and egress_id:
        try:
            logger.info(f"Stopping recording with egress ID: {egress_id}")
            success = await gcp_manager.stop_recording(egress_id)
            if success:
                logger.info("✅ Recording stopped successfully")
            else:
                logger.warning("⚠️ Recording stop returned false")
        except Exception as e:
            logger.error(f"❌ Error stopping recording: {e}")
    
    # Step 2: Stop room recording by room name if available
    if gcp_manager and not egress_id:
        try:
            active_recordings = gcp_manager.get_active_recordings()
            if active_recordings:
                logger.info(f"Found {len(active_recordings)} active recordings to cleanup")
                for room_name, recording_egress_id in active_recordings.items():
                    try:
                        await gcp_manager.stop_recording(recording_egress_id)
                        logger.info(f"✅ Stopped recording for room: {room_name}")
                    except Exception as e:
                        logger.error(f"❌ Failed to stop recording for room {room_name}: {e}")
        except Exception as e:
            logger.error(f"❌ Error during bulk recording cleanup: {e}")
    
    # Step 3: Session cleanup (handled automatically by LiveKit)
    if session:
        try:
            # Stop silence monitoring if agent has it
            if hasattr(session, 'agent') and hasattr(session.agent, 'stop_silence_monitoring'):
                session.agent.stop_silence_monitoring()
                logger.info("✅ Silence monitoring stopped")

            # Stop timing monitoring if agent has it
            if hasattr(session, 'agent') and hasattr(session.agent, 'stop_timing_monitoring'):
                session.agent.stop_timing_monitoring()
                logger.info("✅ Timing monitoring stopped")

            # LiveKit handles session cleanup automatically
            # We just log the completion and any additional cleanup needed
            logger.info("✅ Session cleanup delegated to LiveKit")

            # Any custom session cleanup can be added here
            # For example: custom state persistence, metrics reporting, etc.

        except Exception as e:
            logger.error(f"❌ Error during session cleanup: {e}")
    
    logger.info("Resource cleanup completed")


async def emergency_cleanup():
    """
    Emergency cleanup function for critical failures.
    Attempts to stop all active recordings and sessions.
    """
    logger.warning("🚨 Emergency cleanup initiated")
    
    try:
        # Initialize GCP manager for emergency cleanup
        gcp_manager = GCPEgressManager()
        
        # Get all active recordings
        active_recordings = gcp_manager.get_active_recordings()
        
        if active_recordings:
            logger.warning(f"Emergency stopping {len(active_recordings)} active recordings")
            for room_name, egress_id in active_recordings.items():
                try:
                    await gcp_manager.stop_recording(egress_id)
                    logger.info(f"🛑 Emergency stopped recording for room: {room_name}")
                except Exception as e:
                    logger.error(f"❌ Failed emergency stop for room {room_name}: {e}")
        else:
            logger.info("No active recordings found during emergency cleanup")
            
    except Exception as e:
        logger.error(f"❌ Emergency cleanup failed: {e}")
    
    logger.warning("🚨 Emergency cleanup completed")


def log_cleanup_summary(
    session_cleaned: bool = False,
    recordings_stopped: int = 0,
    errors_encountered: int = 0
):
    """
    Log a summary of the cleanup operation.
    
    Args:
        session_cleaned: Whether session was cleaned up
        recordings_stopped: Number of recordings stopped
        errors_encountered: Number of errors during cleanup
    """
    logger.info("=" * 50)
    logger.info("CLEANUP SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Session cleaned: {'✅' if session_cleaned else '❌'}")
    logger.info(f"Recordings stopped: {recordings_stopped}")
    logger.info(f"Errors encountered: {errors_encountered}")
    
    if errors_encountered == 0:
        logger.info("🎉 Cleanup completed successfully")
    else:
        logger.warning(f"⚠️ Cleanup completed with {errors_encountered} errors")
    
    logger.info("=" * 50)
