# LiveKit POC

## Run Backend Server

- `cd server`
- `pip install -r requirements.txt`
- `python agent.py download-files` to download turn detector files for multilingual
- `python agent.py dev`


## Python setup venv
- python3 -m venv path/to/venv 
- source path/to/venv/bin/activate

# Server
- ` sudo su - azureuser`
- `cd livekit-poc/server`
- `pip install -r requirements.txt`
- `sudo systemctl restart livekit.service`

 
## To Check Logs
sudo journalctl -u livekit.service -f

### To change service inside server
sudo nano /etc/systemd/system/livekit.service
sudo systemctl daemon-reexec
sudo systemctl daemon-reload
sudo systemctl restart livekit.service